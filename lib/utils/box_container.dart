import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' as r;
import 'package:schnell_pole_installation/survey/survey_controller.dart';
import 'package:schnell_pole_installation/utils/constants.dart';
import 'package:schnell_pole_installation/utils/custom_painter.dart';
import 'package:toggle_switch/toggle_switch.dart';

class BoxContainer {
  static Widget boxContainer(context, value) {
    return Card(
        color: const Color.fromARGB(248, 64, 124, 161).withOpacity(0.8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15.0),
        ),
        child: SizedBox(
          height: MediaQuery.of(context).size.height / 6.5,
          width: MediaQuery.of(context).size.width / 4,
          child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Center(
                child: Text(
                  value,
                  style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 17.0),
                ),
              )),
        ));
  }

  static Widget rectangleContainer(value) {
    return Container(
      height: 50,
      decoration: BoxDecoration(
          color: const Color.fromARGB(248, 64, 124, 161).withOpacity(0.8),
          borderRadius: BorderRadius.circular(18)),
      child: Center(
        child: Row(mainAxisAlignment: MainAxisAlignment.center, children: [
          const SizedBox(
            width: 5,
          ),
          const Icon(Icons.location_on_outlined, color: Colors.white),
          const SizedBox(
            width: 5,
          ),
          Text(
            value,
            style: const TextStyle(
                color: Colors.white, fontWeight: FontWeight.bold),
          ),
          const SizedBox(
            width: 10,
          ),
        ]),
      ),
    );
  }

  static Widget numberContainer(value, IconData icon) {
    return FittedBox(
      child: Container(
        height: 50,
        decoration: BoxDecoration(
            color: const Color.fromARGB(248, 64, 124, 161).withOpacity(0.8),
            borderRadius: BorderRadius.circular(40)),
        child: Center(
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              // mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  color: Colors.white,
                  size: 21,
                ),
                const SizedBox(
                  width: 5,
                ),
                Text(
                  value.toString(),
                  style: const TextStyle(
                      color: Colors.white, fontWeight: FontWeight.bold),
                ),
                const SizedBox(
                  width: 10,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  static Widget buildDropdown(
    BuildContext context, {
    String? value,
    required String? label,
    required List<String> items,
    required void Function(String?) onChanged,
  }) {
    // Ensure value is valid or set to null if not found
    final validValue = (value != null && items.contains(value)) ? value : null;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 12.0),
      child: DropdownButtonFormField2<String>(
        isExpanded: true,
        value: validValue,
        decoration: InputDecoration(
          labelText: label,
          labelStyle: TextStyle(
              fontSize: 17, color: darkBlue, fontWeight: FontWeight.bold),
          floatingLabelBehavior: FloatingLabelBehavior.always,
          border: const OutlineInputBorder(),
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        ),
        items: items.toSet().map((item) {
          return DropdownMenuItem<String>(
            value: item,
            child: Text(
              item,
              style: const TextStyle(fontSize: 12, color: Colors.black),
            ),
          );
        }).toList(),
        buttonStyleData: const ButtonStyleData(height: 41),
        onChanged: onChanged,
        dropdownStyleData: DropdownStyleData(
          elevation: 2,
          offset: const Offset(0, 5),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        menuItemStyleData: MenuItemStyleData(
          height: 48,
          overlayColor: WidgetStatePropertyAll(Colors.grey.shade100),
        ),
      ),
    );
  }

  static Widget yesNoToggle(
      r.WidgetRef ref, BuildContext context, String label, selectedValue) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 6),
            ToggleSwitch(
              minWidth: 80,
              cornerRadius: 12,
              activeBgColors: [
                [const Color.fromARGB(248, 64, 124, 161).withOpacity(0.8)],
                [const Color.fromARGB(248, 64, 124, 161).withOpacity(0.8)]
              ],
              activeFgColor: Colors.white,
              inactiveBgColor: Colors.grey[300],
              inactiveFgColor: Colors.black,
              totalSwitches: 2,
              labels: const ['Yes', 'No'],
              initialLabelIndex: selectedValue ? 0 : 1,
              onToggle: (index) {
                final isYes = index == 0;
                ref
                    .read(surveyController)
                    .updateSurveyFieldValues(context, ref, isYes, label);
              },
            ),
          ],
        ),
      ),
    );
  }

  static Widget dateContainer(value, mode, width) {
    return Container(
      height: 50,
      margin: const EdgeInsets.only(top: 5, bottom: 5),
      width: width * 0.32,
      decoration: BoxDecoration(
          color: const Color.fromARGB(248, 64, 124, 161).withOpacity(0.8),
          borderRadius: BorderRadius.circular(40)),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(mainAxisAlignment: MainAxisAlignment.center, children: [
              const SizedBox(
                width: 15,
              ),
              Text(
                value,
                style: const TextStyle(
                    color: Colors.white, fontWeight: FontWeight.bold),
              ),
              const SizedBox(
                width: 15,
              ),
            ]),
            mode == 0
                ? const Text('(Today)',
                    style: TextStyle(
                        color: Colors.white, fontWeight: FontWeight.bold))
                : Container(),
          ],
        ),
      ),
    );
  }

  static Widget customContainer(value) {
    return RotatedBox(
      quarterTurns: 3,
      child: CustomPaint(
        painter: CustomShape(),
        child: SizedBox(
          width: 60.0,
          height: 100.0,
          child: Padding(
            padding: const EdgeInsets.only(top: 30.0, right: 10),
            child: RotatedBox(
              quarterTurns: 1,
              child: Align(
                alignment: Alignment.center,
                child: Text(value,
                    style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 17)),
              ),
            ),
          ),
        ),
      ),
    );
  }

  static Widget customImageContainer(value, mode) {
    if (mode == 1 || mode == 2 || mode == 3) {
      return Stack(alignment: Alignment.center, children: [
        SizedBox(height: 60, child: Image.asset(fullcustomShape)),
        Padding(
          padding: const EdgeInsets.only(left: 60),
          child: Text(value,
              textAlign: TextAlign.center,
              style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 17)),
        )
      ]);
    } else if (mode == 0) {
      return Stack(alignment: Alignment.center, children: [
        SizedBox(height: 60, child: Image.asset(bottomcustomShape)),
        Padding(
          padding: const EdgeInsets.only(left: 60),
          child: Text(value,
              textAlign: TextAlign.center,
              style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 17)),
        )
      ]);
    } else {
      return Stack(alignment: Alignment.center, children: [
        SizedBox(height: 60, child: Image.asset(topcustomShape)),
        Padding(
          padding: const EdgeInsets.only(left: 60),
          child: Text(value,
              textAlign: TextAlign.center,
              style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 17)),
        )
      ]);
    }
  }

  static Widget dropdownValueContainer(width, value, type) {
    return SizedBox(
      width: width,
      child: InputDecorator(
        decoration: InputDecoration(
          label: RichText(
              text: TextSpan(
            text: type,
            style: TextStyle(
                color: darkBlue, fontSize: 20, fontWeight: FontWeight.bold),
          )),
          labelStyle:
              TextStyle(color: Colors.black.withOpacity(0.7), fontSize: 20),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10.0),
          ),
        ),
        child: Text(
          value,
          style: const TextStyle(fontSize: 16),
        ),
      ),
    );
  }
}
