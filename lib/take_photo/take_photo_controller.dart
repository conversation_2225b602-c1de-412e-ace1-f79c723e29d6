import 'dart:developer';
import 'package:schnell_pole_installation/DB/db_repo.dart';
import 'package:schnell_pole_installation/DB/map_model.dart';
import 'package:schnell_pole_installation/take_photo/take_photo_service.dart';

class TakePhotoController {
  final TakePhotoService _service = TakePhotoService();

  /// New method that tries S3 upload first, then falls back to API
  imageUpdationS3(deviceImage, token, imageData, context) async {
    // Try S3 direct upload first
    var result = await _service.updateImageS3(
        deviceImage, imageData['fileName'], context);

    // Fallback to API if S3 fails
    if (result == 'Something Went Wrong' || result == "400") {
      log('S3 upload failed, falling back to API upload for: ${imageData['fileName']}');
      result = await _service.updateImage(deviceImage, token, imageData['name'],
          imageData['fileName'], context);
      log('API upload result for ${imageData['fileName']}: $result');
    }

    if (result == "200") {
      log('${imageData['name']} image uploaded successfully');
      var deleteDbData = await DatabaseRepo()
          .deleteTableDataByPoleName('tbl_image_db', imageData['name']);
    } else if (result == "400") {
      var poleCountData = PoleCountModel();
      poleCountData.deviceImage = deviceImage;
      poleCountData.imagePoleName = imageData['name'];
      poleCountData.imageFileName = imageData['fileName'];
      poleCountData.wtrmrkLatitude = imageData['latitude'];
      poleCountData.wtrmrkLongitude = imageData['longitude'];
      poleCountData.wtrmrkAccuracy = imageData['accuracy'];
      poleCountData.wtrmrkManualEnteredLocation =
          imageData['manualEnteredLocation'];
      poleCountData.wtrmrkLocation = imageData['location'];
      poleCountData.wtrmrkRegion = imageData['region'];
      poleCountData.wtrmrkZone = imageData['zoneName'];
      poleCountData.wtrmrkWard = imageData['wardName'];
      var duplicateDbData = await DatabaseRepo()
          .insertImageData('tbl_duplicateimage_db', poleCountData);
      var deleteDbData = await DatabaseRepo()
          .deleteTableDataByPoleName('tbl_image_db', imageData['name']);
    }
  }

  /// Original method (kept for backward compatibility)
  imageUpdation(deviceImage, token, imageData, context) async {
    var result = await _service.updateImage(
        deviceImage, token, imageData['name'], imageData['fileName'], context);
    if (result == "200") {
      log('${imageData['name']} image uploaded successfully');
      var deleteDbData = await DatabaseRepo()
          .deleteTableDataByPoleName('tbl_image_db', imageData['name']);
    } else if (result == "400") {
      var poleCountData = PoleCountModel();
      poleCountData.deviceImage = deviceImage;
      poleCountData.imagePoleName = imageData['name'];
      poleCountData.imageFileName = imageData['fileName'];
      poleCountData.wtrmrkLatitude = imageData['latitude'];
      poleCountData.wtrmrkLongitude = imageData['longitude'];
      poleCountData.wtrmrkAccuracy = imageData['accuracy'];
      poleCountData.wtrmrkManualEnteredLocation =
          imageData['manualEnteredLocation'];
      poleCountData.wtrmrkLocation = imageData['location'];
      poleCountData.wtrmrkRegion = imageData['region'];
      poleCountData.wtrmrkZone = imageData['zoneName'];
      poleCountData.wtrmrkWard = imageData['wardName'];
      var duplicateDbData = await DatabaseRepo()
          .insertImageData('tbl_duplicateimage_db', poleCountData);
      var deleteDbData = await DatabaseRepo()
          .deleteTableDataByPoleName('tbl_image_db', imageData['name']);
    }
  }
}
