import 'dart:convert';
import 'dart:developer';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:schnell_pole_installation/utils/dio_client.dart';
import 'package:schnell_pole_installation/utils/s3_config.dart';
import '../env.dart';
import '../survey/s3_upload_service.dart';

class TakePhotoService {
  String? status;

  /// Upload image directly to S3
  Future<String> updateImageS3(
      String deviceImage, String fileName, BuildContext context) async {
    try {
      log('Starting S3 upload for file: $fileName');

      // Use the new S3 upload service
      final result = await S3UploadService.uploadImageToS3(
        base64Image: deviceImage,
        fileName: fileName,
        folder: S3Config.luminatorFolder,
        contentType: 'image/jpeg',
      );

      log('S3 upload result: $result');

      // Check if upload was successful
      if (result != null && result.isNotEmpty) {
        log('S3 upload successful, URL: $result');
        return "200";
      } else {
        log('S3 upload failed - result is null or empty');
        return "400";
      }
    } catch (e) {
      log('S3 upload error: $e');
      log('S3 upload error type: ${e.runtimeType}');
      return 'Something Went Wrong';
    }
  }

  /// Original API upload method (fallback)
  Future<String> updateImage(
      deviceImage, token, poleNumber, fileName, context) async {
    var data = {
      "name": fileName,
      "activity": "INSTALL",
      "auditImg": deviceImage
    };
    Dio dio = DioClient.dio;
    try {
      Response response = await dio.post('$baseUrl/api/image/upload/',
          data: data,
          options: Options(contentType: 'application/json', headers: {
            // "token": token,
          }));
      if (response.data != "") {
        var result = jsonDecode(response.data);
        status = result['status'].toString();
      } else {
        //  snackBarLoader('No Data Found',context);
      }
      return status ?? 'Unknown';
    } catch (e) {
      return 'Something Went Wrong';
    }
  }
}
